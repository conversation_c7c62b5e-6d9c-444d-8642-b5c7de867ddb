/**
 * Fetches a short-lived access token for a specified Google Cloud service account
 * via impersonation. This token is used to authenticate subsequent API calls to
 * Google Cloud services like Firestore, adhering to keyless authentication best practices.
 *
 * Requires the `https://www.googleapis.com/auth/iam` and
 * `https://www.googleapis.com/auth/script.external_request` OAuth scopes.
 *
 * @param {string} serviceAccountEmail The email address of the service account to impersonate
 *   (e.g., `<EMAIL>`).
 * @param {Array<string>} scopes An array of OAuth scopes that the generated token should have
 *   (e.g., `['https://www.googleapis.com/auth/datastore']` for Firestore access).
 * @returns {string} The generated access token.
 * @throws {Error} If the token fetching API call fails or returns an error.
 */
function getServiceAccountToken(serviceAccountEmail, scopes) {
  const url = `https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/${serviceAccountEmail}:generateAccessToken`;

  const payload = {
    scope: scopes,
    lifetime: '3600s' // Token will be valid for 1 hour (max allowed by API)
  };

  const options = {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      'Authorization': 'Bearer ' + ScriptApp.getOAuthToken() // Authorize with the user's Apps Script token
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true // Important to catch HTTP errors in the try/catch block
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    if (responseCode >= 200 && responseCode < 300) {
      const token = JSON.parse(responseText).accessToken;
      return token;
    } else {
      // Log and throw an error if the API call was unsuccessful
      throw new Error(`API Error: ${responseCode} - ${responseText}`);
    }
  } catch (error) {
    Log.error(`Error fetching token: ${error.message}`, { stack: error.stack, serviceAccount: serviceAccountEmail, scopes: scopes });
    throw new Error(`Failed to get token for service account '${serviceAccountEmail}': ${error.message}`);
  }
}


/**
 * Saves an array of chunk objects to Google Cloud Firestore, writing each document individually
 * for increased resilience to failures. Documents are created with a unique ID within the
 * specified collection. This function handles authentication via a service account token and
 * provides a detailed report of successful and failed operations.
 *
 * @param {Array<Object>} chunks The array of chunk objects to save. Each object should represent
 *   a document to be stored in Firestore.
 * @param {string} collectionName The name of the Firestore collection to save the chunks to.
 * @returns {Object} A report detailing the outcome of the save operation, including:
 *   - `successful`: The number of chunks successfully added to batches and committed.
 *   - `failed`: The number of chunks that failed to be saved.
 *   - `errors`: An array of error objects for failed batches, including messages, stack traces,
 *     and a preview of the chunks that caused the failure.
 *
 */
function saveChunksToFirestore(chunks, collectionName) {
  const projectId = FIRESTORE_PROJECT_ID;

  if (!projectId) {
    const errorMsg = "Firestore project ID not found in Config.js. Please set 'FIRESTORE_PROJECT_ID'.";
    Log.critical(errorMsg); // Critical error as Firestore cannot be accessed
    return { successful: 0, failed: chunks.length, errors: [{ message: errorMsg, chunksAttempted: chunks.length }] };
  }

  // Service account email must have 'Cloud Datastore User' or 'Cloud Datastore Owner' role
  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access:", { message: e.message });
    return { successful: 0, failed: chunks.length, errors: [{ message: `Failed to get service account token: ${e.message}`, chunksAttempted: chunks.length }] };
  }

  // Initialize FirestoreApp with the dynamically obtained access token
  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  const saveReport = {
    successful: 0,
    failed: 0,
    errors: []
  };

  // Process each chunk individually to ensure resilience to failures
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    try {
      const docId = Utilities.getUuid(); // Generate a unique ID for each document
      const docPath = `${collectionName}/${docId}`; // Use the provided collectionName
      firestore.createDocument(docPath, chunk); // Create document individually
      saveReport.successful++;
      Log.info(`Successfully saved chunk ${i + 1}/${chunks.length} to Firestore collection: ${collectionName}.`);
    } catch (e) {
      saveReport.failed++;
      const errorMessage = `Failed to save chunk index ${i} to collection ${collectionName}: ${e.message}`;
      Log.error(errorMessage, {
        stack: e.stack,
        chunkIndex: i,
        chunkContentPreview: chunk ? chunk.content.substring(0, 100) : 'N/A'
      });
      saveReport.errors.push({
        message: errorMessage,
        stack: e.stack,
        chunkIndex: i,
        chunkContentPreview: chunk ? chunk.content.substring(0, 100) : 'N/A'
      });
      // Continue processing subsequent chunks even if one fails
    }
  }
  return saveReport;
}

/**
 * Deletes all documents within a specified Firestore collection.
 * This function is intended for clearing test data or resetting collections.
 *
 * @param {string} collectionName The name of the Firestore collection to clear.
 * @returns {boolean} True if the operation was successful, false otherwise.
 */
function clearFirestoreCollection_(collectionName) {
  const projectId = FIRESTORE_PROJECT_ID;

  if (!projectId) {
    Log.error("Firestore project ID not found in Config.js. Cannot clear collection.");
    return false;
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to clear collection:", { message: e.message });
    return false;
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  const batchSize = 200; // Max batch delete size for Firestore
  let documentsDeleted = 0;

  try {
    // Use the new helper function to list documents
    const documentNames = listFirestoreCollectionDocuments(projectId, collectionName, accessToken);

    if (documentNames.length > 0) {
      // Delete documents in batches
      for (let i = 0; i < documentNames.length; i += batchSize) {
        const batchToDelete = documentNames.slice(i, i + batchSize);
        const batch = firestore.batch();
        batchToDelete.forEach(fullDocPath => {
          // Extract the document ID from the full resource path
          const parts = fullDocPath.split('/');
          const docId = parts[parts.length - 1];
          // Construct the relative path expected by FirestoreApp.batch().deleteDocument()
          const docPath = `${collectionName}/${docId}`;
          batch.deleteDocument(docPath);
        });
        batch.commit();
        documentsDeleted += batchToDelete.length;
        Log.info(`Deleted batch of ${batchToDelete.length} documents from collection: ${collectionName}. Total deleted: ${documentsDeleted}`);
      }
    }

    Log.info(`Successfully cleared collection: ${collectionName}. Total documents deleted: ${documentsDeleted}`);
    return true;
  } catch (e) {
    Log.error(`Error clearing collection ${collectionName}: ${e.message}`, { stack: e.stack });
    return false;
  }
}

/**
 * Fetches all unique message IDs from documents in a specified Firestore collection.
 * This function is optimized to only retrieve the 'message_ids' field, minimizing data transfer.
 *
 * @param {string} collectionName The name of the Firestore collection (e.g., 'clustered_summaries').
 * @returns {Set<string>} A Set containing all unique message IDs found in the collection.
 * @throws {Error} If the Firestore project ID is not configured or token fetching fails.
 */
function getProcessedMessageIds(collectionName) {
  const projectId = FIRESTORE_PROJECT_ID;

  if (!projectId) {
    const errorMsg = "Firestore project ID not found in Config.js. Cannot retrieve processed message IDs.";
    Log.critical(errorMsg);
    throw new Error(errorMsg);
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to retrieve message IDs:", { message: e.message });
    throw new Error(`Failed to get token for service account: ${e.message}`);
  }

  const processedMessageIds = new Set();
  let nextPageToken = null;
  const pageSize = 1000; // Max page size for Firestore list API

  // Base URL for listing documents in the collection
  const baseUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collectionName}`;

  const options = {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    },
    muteHttpExceptions: true
  };

  do {
    let url = baseUrl;
    const params = [];
    params.push(`pageSize=${pageSize}`);
    // Only select the 'message_ids' field to minimize data transfer
    params.push(`mask.fieldPaths=message_ids`);
    if (nextPageToken) {
      params.push(`pageToken=${nextPageToken}`);
    }
    url += `?${params.join('&')}`;

    try {
      const response = UrlFetchApp.fetch(url, options);
      const responseCode = response.getResponseCode();
      const responseText = response.getContentText();

      if (responseCode >= 200 && responseCode < 300) {
        const responseJson = JSON.parse(responseText);
        if (responseJson.documents) {
          responseJson.documents.forEach(doc => {
            if (doc.fields && doc.fields.message_ids && doc.fields.message_ids.arrayValue && doc.fields.message_ids.arrayValue.values) {
              doc.fields.message_ids.arrayValue.values.forEach(idValue => {
                if (idValue.stringValue) {
                  processedMessageIds.add(idValue.stringValue);
                }
              });
            }
          });
        }
        nextPageToken = responseJson.nextPageToken;
      } else {
        throw new Error(`Firestore API Error (getProcessedMessageIds): ${responseCode} - ${responseText}`);
      }
    } catch (e) {
      Log.error(`Error listing documents for processed message IDs in collection ${collectionName}: ${e.message}`, { stack: e.stack });
      throw new Error(`Failed to list documents for processed message IDs in collection '${collectionName}': ${e.message}`);
    }
  } while (nextPageToken);

  Log.info(`Retrieved ${processedMessageIds.size} unique processed message IDs from collection: ${collectionName}.`);
  return processedMessageIds;
}

/**
 * Fetches all documents and their full content from a specified Firestore collection.
 * This function is designed to retrieve all data for a collection, which can then be
 * used for operations like semantic search with Gemini.
 *
 * @param {string} collectionName The name of the Firestore collection to retrieve documents from.
 * @returns {Array<Object>} An array of document objects, each containing the document's ID and fields.
 * @throws {Error} If the Firestore project ID is not configured or token fetching fails.
 */
function getCollectionDocuments(collectionName) {
  const projectId = FIRESTORE_PROJECT_ID;

  if (!projectId) {
    const errorMsg = "Firestore project ID not found in Config.js. Cannot retrieve collection documents.";
    Log.critical(errorMsg);
    throw new Error(errorMsg);
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to retrieve collection documents:", { message: e.message });
    throw new Error(`Failed to get token for service account: ${e.message}`);
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  const documents = [];

  try {
    // First, list all document names (full resource paths) in the collection
    const documentNames = listFirestoreCollectionDocuments(projectId, collectionName, accessToken);

    // Then, fetch the full content of each document
    documentNames.forEach(fullDocPath => {
      // Extract the relative path (e.g., "collectionName/docId")
      const pathParts = fullDocPath.split('/');
      const relativeDocPath = `${pathParts[pathParts.length - 2]}/${pathParts[pathParts.length - 1]}`;

      try {
        const doc = firestore.getDocument(relativeDocPath);
        if (doc) {
          // Add the document ID and its fields to the results array
          documents.push({ id: pathParts[pathParts.length - 1], fields: doc.fields });
        }
      } catch (e) {
        Log.warn(`Failed to retrieve full content for document ${relativeDocPath}: ${e.message}`);
        // Continue to next document even if one fails
      }
    });

    Log.info(`Retrieved ${documents.length} documents from collection: ${collectionName}.`);
    return documents;
  } catch (e) {
    Log.error(`Error retrieving documents from collection ${collectionName}: ${e.message}`, { stack: e.stack });
    throw new Error(`Failed to retrieve documents from collection '${collectionName}': ${e.message}`);
  }
}



/**
 * Lists all document names (full resource paths) within a specified Firestore collection
 * by making direct REST API calls. Handles pagination.
 *
 * @param {string} projectId The Google Cloud Project ID.
 * @param {string} collectionName The name of the Firestore collection.
 * @param {string} accessToken The access token for authentication.
 * @returns {Array<string>} An array of full document resource names (e.g., "projects/projectId/databases/(default)/documents/collectionName/docId").
 * @throws {Error} If the API call fails.
 */
function listFirestoreCollectionDocuments(projectId, collectionName, accessToken) {
  const documents = [];
  let nextPageToken = null;
  const pageSize = 1000; // Max page size for Firestore list API

  const baseUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/${collectionName}`;

  const options = {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    },
    muteHttpExceptions: true
  };

  do {
    let url = baseUrl;
    const params = [];
    params.push(`pageSize=${pageSize}`);
    if (nextPageToken) {
      params.push(`pageToken=${nextPageToken}`);
    }
    url += `?${params.join('&')}`;

    try {
      const response = UrlFetchApp.fetch(url, options);
      const responseCode = response.getResponseCode();
      const responseText = response.getContentText();

      if (responseCode >= 200 && responseCode < 300) {
        const responseJson = JSON.parse(responseText);
        if (responseJson.documents) {
          responseJson.documents.forEach(doc => documents.push(doc.name));
        }
        nextPageToken = responseJson.nextPageToken;
      } else {
        throw new Error(`Firestore API Error (listDocuments): ${responseCode} - ${responseText}`);
      }
    } catch (e) {
      Log.error(`Error listing documents in collection ${collectionName}: ${e.message}`, { stack: e.stack });
      throw new Error(`Failed to list documents in collection '${collectionName}': ${e.message}`);
    }
  } while (nextPageToken);

  return documents;
}

/**
 * Deletes the last processed timestamp entry for a specific chat space from Firestore metadata.
 * This is useful for resetting the processing state for a particular space, e.g., for testing.
 *
 * @param {string} spaceId The ID of the chat space for which to delete metadata.
 * @returns {boolean} True if the deletion was successful, false otherwise.
 */
function deleteMetadataForSpace(spaceId) {
  const projectId = FIRESTORE_PROJECT_ID;
  const DOC_ID = 'chat_space_timestamps';

  if (!projectId) {
    Log.error("Firestore project ID not found in Config.js. Cannot delete metadata.");
    return false;
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to delete metadata:", { message: e.message });
    return false;
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  try {
    const docPath = `${METADATA_COLLECTION_NAME}/${DOC_ID}`;
    // To delete a specific field, we need to read the document, remove the field, and then update.
    // Firestore's update method with a field path can also be used to delete a field.
    // Alternatively, fetch the document, remove the field, and then update the document.

    // For simplicity and to avoid needing a new FirestoreApp method, we'll fetch and update.
    const currentDoc = firestore.getDocument(docPath);
    if (currentDoc && currentDoc.fields) {
      const fieldsToUpdate = { ...currentDoc.fields };
      if (fieldsToUpdate[spaceId]) {
        delete fieldsToUpdate[spaceId]; // Remove the field
        const batch = firestore.batch();
        batch.updateDocument(docPath, fieldsToUpdate); // Update the document without the field
        batch.commit();
        Log.info(`Successfully deleted metadata for space ${spaceId}.`);
        return true;
      } else {
        Log.info(`No metadata found for space ${spaceId} to delete.`);
        return true; // Considered successful if nothing to delete
      }
    } else {
      Log.info(`Metadata document ${docPath} not found. Nothing to delete for space ${spaceId}.`);
      return true; // Considered successful if document doesn't exist
    }
  } catch (e) {
    Log.error(`Error deleting metadata for space ${spaceId}: ${e.message}`, { stack: e.stack });
    return false;
  }
}

/**
 * Fetches the last processed timestamp for a given chat space from Firestore metadata.
 * @param {string} spaceId The ID of the chat space.
 * @returns {string|null} The ISO string timestamp of the last processed message, or null if not found.
 */
function getLastProcessedTimestamp(spaceId) {
  const projectId = FIRESTORE_PROJECT_ID;
  const DOC_ID = 'chat_space_timestamps'; // A single document to store all space timestamps

  if (!projectId) {
    Log.error("Firestore project ID not found in Config.js. Cannot get last processed timestamp.");
    return null;
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to get timestamp:", { message: e.message });
    return null;
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  try {
    const docPath = `${METADATA_COLLECTION_NAME}/${DOC_ID}`;
    const response = firestore.getDocument(docPath); // Assuming getDocument exists or can be added to FirestoreApp

    if (response && response.fields && response.fields[spaceId] && response.fields[spaceId].stringValue) {
      const timestamp = response.fields[spaceId].stringValue;
      Log.info(`Retrieved last processed timestamp for ${spaceId}: ${timestamp}`);
      return timestamp;
    } else {
      Log.info(`No last processed timestamp found for ${spaceId}.`);
      return null;
    }
  } catch (e) {
    Log.error(`Error fetching last processed timestamp for ${spaceId}: ${e.message}`, { stack: e.stack });
    return null;
  }
}

/**
 * Updates the last processed timestamp for a given chat space in Firestore metadata.
 * @param {string} spaceId The ID of the chat space.
 * @param {string} timestamp The ISO string timestamp of the most recent message processed.
 * @returns {boolean} True if the update was successful, false otherwise.
 */
function updateLastProcessedTimestamp(spaceId, timestamp) {
  const projectId = FIRESTORE_PROJECT_ID;
  const DOC_ID = 'chat_space_timestamps';

  if (!projectId) {
    Log.error("Firestore project ID not found in Config.js. Cannot update last processed timestamp.");
    return false;
  }

  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access to update timestamp:", { message: e.message });
    return false;
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  try {
    const docPath = `${METADATA_COLLECTION_NAME}/${DOC_ID}`;
    const fieldsToUpdate = {};
    fieldsToUpdate[spaceId] = { stringValue: timestamp };

    const batch = firestore.batch();

    // Check if the document exists. If getDocument throws, it means it doesn't exist.
    let docExists = false;
    try {
      const existingDoc = firestore.getDocument(docPath);
      if (existingDoc && existingDoc.name) { // Check for existence based on the 'name' property of the document
        docExists = true;
      }
    } catch (e) {
      // If getDocument throws an error, it means the document doesn't exist.
      // This is expected for the first run, so log as debug.
      Log.debug(`Metadata document ${docPath} not found during update check. Will attempt to create.`, { error: e.message });
      docExists = false;
    }

    if (docExists) {
      // If document exists, update it with the new field using an update mask
      batch.updateDocument(docPath, fieldsToUpdate, true);
    } else {
      // If document does not exist, create it with the initial field
      batch.createDocument(docPath, fieldsToUpdate);
    }
    batch.commit();

    Log.info(`Successfully updated last processed timestamp for ${spaceId} to ${timestamp}.`);
    return true;
  } catch (e) {
    Log.error(`Error updating last processed timestamp for ${spaceId}: ${e.message}`, { stack: e.stack });
    return false;
  }
}
