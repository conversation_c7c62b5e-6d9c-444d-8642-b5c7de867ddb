// Assuming GEMINI_MODEL and GEMINI_API_KEY are defined in Config.js and globally available.
// Assuming GEMINI_MODEL and GEMINI_API_KEY are defined in Config.js and globally available.
// Assuming Log object is available from Logging.js.

/******************************************************************************
 *
 * Helper Function: Exponential Backoff Retry
 *
 * Retries a given function with exponential backoff.
 *
 * @param {Function} func The function to retry.
 * @param {Array} args Arguments to pass to the function.
 * @param {number} maxRetries The maximum number of retries.
 * @param {number} initialDelayMs The initial delay in milliseconds.
 * @param {string} functionName The name of the function being retried for logging.
 * @return {any} The result of the function if successful.
 * @throws {Error} If the function fails after all retries.
 * @private
 ******************************************************************************/
function retryWithExponentialBackoff_(func, args, maxRetries, initialDelayMs, functionName) {
  let delay = initialDelayMs;
  for (let i = 0; i <= maxRetries; i++) {
    try {
      const result = func(...args);
      if (result !== null) { // Assuming null indicates a recoverable error or API issue
        return result;
      }
    } catch (e) {
      Log.warn(`${functionName}: Attempt ${i + 1}/${maxRetries + 1} failed with exception: ${e.message}`);
      if (i === maxRetries) {
        throw e; // Re-throw if it's the last retry
      }
    }

    if (i < maxRetries) {
      Log.info(`${functionName}: Retrying in ${delay / 1000} seconds...`);
      Utilities.sleep(delay);
      delay *= 2; // Exponential increase
    }
  }
  throw new Error(`${functionName}: Failed after ${maxRetries + 1} attempts.`);
}

/******************************************************************************
 *
 * Main Orchestration Function (Map-Reduce Workflow)
 *
 * This function implements a Map-Reduce strategy to cluster a large number
 * of messages that may exceed the context window of a single API call.
 *
 * @param {Array<Object>} allMessages An array of all message objects to cluster.
 *   e.g., [{id: "msg1", text: "Hello world"}, {id: "msg2", text: "Another message"}].
 * @param {number} [chunkSize=MAX_MESSAGES_PER_CHUNK] The number of messages to include in each chunk.
 *   Choose a size that ensures the total token count per chunk is safely
 *   within the model's context window limit.
 * @return {Array<Object>|null} An array of the final, consolidated cluster
 *   objects, or null if an error occurs.
 *
 ******************************************************************************/
function clusterLargeDataset(allMessages, chunkSize = MAX_MESSAGES_PER_CHUNK) {
  // 1. CHUNK DATA: Split the large message array into smaller chunks.
  const messageChunks = chunkArray_(allMessages, chunkSize);
  Log.info(`Split ${allMessages.length} messages into ${messageChunks.length} chunks using chunk size ${chunkSize}.`);

  let allLocalClusters = [];
  let allTopicNames = new Set();

  // 2. MAP STAGE: Process each chunk independently to get "local" clusters.
  messageChunks.forEach((chunk, index) => {
    Log.debug(`Processing chunk ${index + 1} of ${messageChunks.length}...`);
    try {
      const localResult = retryWithExponentialBackoff_(
        clusterSingleChunk_,
        [chunk],
        3, // maxRetries
        1000, // initialDelayMs (1 second)
        'clusterSingleChunk_'
      );

      if (localResult && localResult.clusters) {
        allLocalClusters = allLocalClusters.concat(localResult.clusters);
        localResult.clusters.forEach(cluster => allTopicNames.add(cluster.cluster_name));
      } else {
        Log.warn(`Chunk ${index + 1} failed to produce clusters after retries.`);
      }
    } catch (e) {
      Log.error(`Chunk ${index + 1} failed permanently after retries: ${e.message}`);
    }
  });

  if (allLocalClusters.length === 0) {
    Log.error('No clusters were generated from any chunk.');
    return null;
  }

  // 3. REDUCE STAGE: Consolidate the local topic names into global topics.
  Log.info('Consolidating topic names...');
  let topicMap = null;
  try {
    topicMap = retryWithExponentialBackoff_(
      consolidateClusterTopics_,
      [Array.from(allTopicNames)],
      3, // maxRetries
      1000, // initialDelayMs (1 second)
      'consolidateClusterTopics_'
    );
  } catch (e) {
    Log.error(`Failed to consolidate topic names after retries: ${e.message}`);
    return null;
  }

  if (!topicMap) {
    Log.error('Failed to consolidate topic names.');
    return null;
  }

  // 4. AGGREGATE RESULTS: Merge local clusters into final global clusters.
  Log.info('Aggregating final clusters...');
  const finalClusters = {};
  let nextGlobalClusterId = 1;

  allLocalClusters.forEach(localCluster => {
    const globalTopicName = topicMap[localCluster.cluster_name] || localCluster.cluster_name;

    if (!finalClusters[globalTopicName]) {
      finalClusters[globalTopicName] = {
        cluster_id: nextGlobalClusterId++,
        cluster_name: globalTopicName,
        message_ids: []
      };
    }
    finalClusters[globalTopicName].message_ids.push(...localCluster.message_ids);
  });

  // Convert the final clusters object into an array and return.
  return Object.values(finalClusters);
}


/******************************************************************************
 *
 * Helper Function: Chunk Array
 *
 * A utility function to split a large array into smaller arrays of a
 * specified size.
 *
 * @param {Array} array The array to be chunked.
 * @param {number} size The size of each chunk.
 * @return {Array<Array>} An array of smaller arrays.
 * @private
 ******************************************************************************/
function chunkArray_(array, size) {
  const chunkedArr = [];
  for (let i = 0; i < array.length; i += size) {
    chunkedArr.push(array.slice(i, i + size));
  }
  return chunkedArr;
}


/******************************************************************************
 *
 * "Map" Function: Cluster a Single Chunk
 *
 * This function takes a single chunk of messages and calls the Gemini API
 * to perform topic clustering on just that chunk.
 *
 * @param {Array<Object>} messageChunk A small array of message objects.
 * @return {Object|null} The parsed JSON response from Gemini for the chunk,
 *   or null on error.
 * @private
 ******************************************************************************/
function clusterSingleChunk_(messageChunk) {
  const API_KEY = GEMINI_API_KEY;
  if (!API_KEY) {
    Log.error('Error: GEMINI_API_KEY not found in Config.js.');
    return null;
  }

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${API_KEY}`;

  const responseSchema = {
    "type": "OBJECT",
    "properties": {
      "clusters": {
        "type": "ARRAY",
        "items": {
          "type": "OBJECT",
          "properties": {
            "cluster_id": { "type": "INTEGER" },
            "cluster_name": {
              "type": "STRING",
              "description": "A short, descriptive name for the topic cluster (e.g., 'Billing Inquiries', 'Feature Requests'). Should be 2-5 words."
            },
            "message_ids": {
              "type": "ARRAY",
              "items": { "type": "STRING" }
            }
          },
          "required": ["cluster_id", "cluster_name", "message_ids"]
        }
      }
    },
    "required": ["clusters"]
  };

  const promptText = `
    You are an expert data analyst specializing in unsupervised topic modeling.
    Your task is to analyze the following JSON array of messages and group them into distinct thematic clusters.
    
    INSTRUCTIONS:
    1. Organically discover topics from the provided message texts. Do not use a predefined list of topics.
    2. Assign each message to exactly one cluster.
    3. Generate a short, descriptive name for each cluster that summarizes its core theme.
    4. The final output must be a single JSON object that strictly adheres to the provided response schema.

    MESSAGE DATA:
    ${JSON.stringify(messageChunk)}
  `;

  const payload = {
    "contents": [{
      "parts": [{
        "text": promptText
      }]
    }],
    "generationConfig": {
      "response_mime_type": "application/json",
      "response_schema": responseSchema,
      "temperature": 0.1
    }
  }

  const options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };

  try {
    Log.info(`clusterSingleChunk_: Making API call to ${API_URL} with payload preview: ${JSON.stringify(payload).substring(0, 200)}...`);
    const response = UrlFetchApp.fetch(API_URL, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    Log.debug(`clusterSingleChunk_: API response received. Code: ${responseCode}. Body preview: ${responseBody.substring(0, 200)}...`);

    if (responseCode >= 200 && responseCode < 300) {
      const responseJson = JSON.parse(responseBody);
      // Extract the JSON string from the 'text' part and parse it again
      if (responseJson.candidates && responseJson.candidates.length > 0 &&
        responseJson.candidates[0].content && responseJson.candidates[0].content.parts &&
        responseJson.candidates[0].content.parts.length > 0 && responseJson.candidates[0].content.parts[0].text) {
        try {
          return JSON.parse(responseJson.candidates[0].content.parts[0].text);
        } catch (parseError) {
          Log.error(`clusterSingleChunk_: Failed to parse nested JSON from Gemini response: ${parseError.message}`, { stack: parseError.stack, responseBody: responseBody, chunkPreview: JSON.stringify(messageChunk).substring(0, 200) });
          return null;
        }
      } else {
        Log.error(`clusterSingleChunk_: Gemini API response did not contain expected content structure.`, { responseBody: responseBody, chunkPreview: JSON.stringify(messageChunk).substring(0, 200) });
        return null;
      }
    } else {
      // For API errors (e.g., 429, 500), return null to trigger retry
      Log.warn(`clusterSingleChunk_: API Error. Code: ${responseCode}. Body: ${responseBody}`, { responseCode: responseCode, responseBody: responseBody, chunkPreview: JSON.stringify(messageChunk).substring(0, 200) });
      return null;
    }
  } catch (e) {
    // For network exceptions, re-throw to be caught by the retry mechanism
    Log.error(`clusterSingleChunk_: Exception during API call: ${e.message}`, { stack: e.stack, chunkPreview: JSON.stringify(messageChunk).substring(0, 200) });
    throw e;
  }
}

/******************************************************************************
 *
 * Main Orchestration Function (Semantic Search Workflow)
 *
 * This function performs a semantic search within a specified Firestore collection
 * using the Gemini API. It retrieves all documents from the collection, sends them
 * to Gemini along with a user query, and returns a ranked list of relevant documents.
 *
 * @param {string} collectionName The name of the Firestore collection to search within.
 * @param {string} query The user's search query.
 * @return {Array<Object>|null} An array of relevant document objects, ranked by
 *   relevance, or null if an error occurs. Each object will contain the document's
 *   ID, a relevance score, and a relevant snippet.
 ******************************************************************************/
function searchCollection(collectionName, query) {
  Log.info(`Starting semantic search in collection: ${collectionName} for query: "${query}"`);

  let allDocuments;
  try {
    // 1. Retrieve all documents from the specified Firestore collection
    allDocuments = retryWithExponentialBackoff_(
      getCollectionDocuments, // Function from FirestoreService.js
      [collectionName],
      3, // maxRetries
      1000, // initialDelayMs (1 second)
      'getCollectionDocuments'
    );
    if (!allDocuments || allDocuments.length === 0) {
      Log.warn(`No documents found in collection: ${collectionName}. Cannot perform search.`);
      return [];
    }
    Log.info(`Successfully retrieved ${allDocuments.length} documents from Firestore for search.`);
  } catch (e) {
    Log.error(`Failed to retrieve documents from Firestore for search: ${e.message}`);
    return null;
  }

  const API_KEY = GEMINI_API_KEY;
  if (!API_KEY) {
    Log.error('Error: GEMINI_API_KEY not found in Config.js.');
    return null;
  }

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${API_KEY}`;

  const responseSchema = {
    "type": "OBJECT",
    "properties": {
      "results": {
        "type": "ARRAY",
        "items": {
          "type": "OBJECT",
          "properties": {
            "document_id": { "type": "STRING" },
            "relevance_score": { "type": "NUMBER", "description": "A score from 0 to 1, where 1 is most relevant." },
            "snippet": { "type": "STRING", "description": "A concise, relevant snippet from the document." }
          },
          "required": ["document_id", "relevance_score", "snippet"]
        }
      }
    },
    "required": ["results"]
  };

  const promptText = `
    You are an expert search engine. Your task is to perform a semantic search
    across the provided JSON array of documents based on the user's query.

    INSTRUCTIONS:
    1. Analyze each document's content for relevance to the query.
    2. For each relevant document, extract the most pertinent snippet.
    3. Assign a relevance score (0-1) to each relevant document.
    4. Return a JSON object that strictly adheres to the provided response schema,
       containing an array of search results, ordered by relevance_score (highest first).
    5. If no documents are relevant, return an empty array for "results".

    USER QUERY: "${query}"

    DOCUMENTS:
    ${JSON.stringify(allDocuments)}
  `;

  const payload = {
    "contents": [{
      "parts": [{
        "text": promptText
      }]
    }],
    "generationConfig": {
      "response_mime_type": "application/json",
      "response_schema": responseSchema,
      "temperature": 0.1 // Keep temperature low for factual search results
    }
  };

  const options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };

  try {
    Log.info(`searchCollection: Making API call to ${API_URL} with payload preview: ${JSON.stringify(payload).substring(0, 200)}...`);
    const response = UrlFetchApp.fetch(API_URL, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    Log.debug(`searchCollection: API response received. Code: ${responseCode}. Body preview: ${responseBody.substring(0, 200)}...`);

    if (responseCode >= 200 && responseCode < 300) {
      const responseJson = JSON.parse(responseBody);
      if (responseJson.candidates && responseJson.candidates.length > 0 &&
        responseJson.candidates[0].content && responseJson.candidates[0].content.parts &&
        responseJson.candidates[0].content.parts.length > 0 && responseJson.candidates[0].content.parts[0].text) {
        try {
          const searchResults = JSON.parse(responseJson.candidates[0].content.parts[0].text);
          if (searchResults && searchResults.results) {
            // Sort results by relevance_score in descending order
            return searchResults.results.sort((a, b) => b.relevance_score - a.relevance_score);
          }
          return []; // Return empty array if no results property
        } catch (parseError) {
          Log.error(`searchCollection: Failed to parse nested JSON from Gemini response: ${parseError.message}`, { stack: parseError.stack, responseBody: responseBody, query: query });
          return null;
        }
      } else {
        Log.error(`searchCollection: Gemini API response did not contain expected content structure.`, { responseBody: responseBody, query: query });
        return null;
      }
    } else {
      Log.warn(`searchCollection: API Error. Code: ${responseCode}. Body: ${responseBody}`, { responseCode: responseCode, responseBody: responseBody, query: query });
      return null;
    }
  } catch (e) {
    Log.error(`searchCollection: Exception during API call: ${e.message}`, { stack: e.stack, query: query });
    throw e;
  }
}

/******************************************************************************
 *
 * Main Orchestration Function (Question Answering from Collection)
 *
 * This function answers a user's question by searching through clustered chat
 * summaries from Firestore and using Gemini to determine if the answer is present
 * in the chat conversation summaries.
 *
 * @param {string} [collectionName=CLUSTERED_COLLECTION_NAME] The name of the Firestore collection to search within. Defaults to the clustered chat summaries collection.
 * @param {string} question The user's question.
 * @return {string|null} The answer to the question if found, "NOT_A_QUESTION" if
 *   the input is not a question, "NOT_FOUND" if the answer is not in the context,
 *   or null if an error occurs.
 ******************************************************************************/
function answerQuestionFromCollection(collectionName = CLUSTERED_COLLECTION_NAME, question) {
  Log.info(`Starting question answering in collection: ${collectionName} for question: "${question}"`);

  let allDocuments;
  try {
    // 1. Retrieve all documents from the specified Firestore collection
    allDocuments = retryWithExponentialBackoff_(
      getCollectionDocuments, // Function from FirestoreService.js
      [collectionName],
      3, // maxRetries
      1000, // initialDelayMs (1 second)
      'getCollectionDocuments'
    );
    if (!allDocuments || allDocuments.length === 0) {
      Log.warn(`No documents found in collection: ${collectionName}. Cannot answer question.`);
      return "NOT_FOUND"; // Or handle as appropriate if no context means no answer
    }
    Log.info(`Successfully retrieved ${allDocuments.length} documents from Firestore for question answering.`);
  } catch (e) {
    Log.error(`Failed to retrieve documents from Firestore for question answering: ${e.message}`);
    return null;
  }

  // Format clustered summary documents into a single context string
  const context = allDocuments.map(doc => {
    // Extract fields from the clustered summary document structure
    const clusterName = doc.fields && doc.fields.cluster_name && doc.fields.cluster_name.stringValue ? doc.fields.cluster_name.stringValue : 'Unknown Topic';
    const summary = doc.fields && doc.fields.summary && doc.fields.summary.stringValue ? doc.fields.summary.stringValue : 'No summary available';
    const messageCount = doc.fields && doc.fields.message_count && doc.fields.message_count.integerValue ? doc.fields.message_count.integerValue : 0;
    const summarizedAt = doc.fields && doc.fields.summarizedAt && doc.fields.summarizedAt.stringValue ? doc.fields.summarizedAt.stringValue : '';

    return `Topic: ${clusterName}
Summary: ${summary}
Message Count: ${messageCount}
Date: ${summarizedAt}`;
  }).join('\n\n---\n\n'); // Separator for clarity

  const API_KEY = GEMINI_API_KEY;
  if (!API_KEY) {
    Log.error('Error: GEMINI_API_KEY not found in Config.js.');
    return null;
  }

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${API_KEY}`;

  const responseSchema = {
    "type": "OBJECT",
    "properties": {
      "answer": {
        "type": "STRING",
        "description": "The answer to the question, 'NOT_A_QUESTION', or 'NOT_FOUND'."
      }
    },
    "required": ["answer"]
  };

  const promptText = `
    Your first task is to determine if the following 'USER TEXT' is a question that can be answered from the 'CONTEXT'.
    The CONTEXT contains summaries of chat conversations organized by topic, including discussion summaries, message counts, and dates.

    - If it is NOT a question, respond with the single word: NOT_A_QUESTION
    - If it IS a question but the answer is NOT in the 'CONTEXT', respond with the single word: NOT_FOUND
    - If it IS a question AND the answer is in the 'CONTEXT', provide the answer directly based on the chat summaries.

    When answering, you can reference specific topics and their summaries. Be concise but informative.

    CONTEXT (Chat Conversation Summaries):
    ---
    ${context}
---

    USER TEXT: ${question}
  `;

  const payload = {
    "contents": [{
      "parts": [{
        "text": promptText
      }]
    }],
    "generationConfig": {
      "response_mime_type": "application/json",
      "response_schema": responseSchema,
      "temperature": 0.1 // Keep temperature low for factual answers
    }
  };

  const options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };

  try {
    Log.info(`answerQuestionFromCollection: Making API call to ${API_URL} with payload preview: ${JSON.stringify(payload).substring(0, 200)}...`);
    const response = UrlFetchApp.fetch(API_URL, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    Log.debug(`answerQuestionFromCollection: API response received. Code: ${responseCode}. Body preview: ${responseBody.substring(0, 200)}...`);

    if (responseCode >= 200 && responseCode < 300) {
      const responseJson = JSON.parse(responseBody);
      if (responseJson.candidates && responseJson.candidates.length > 0 &&
        responseJson.candidates[0].content && responseJson.candidates[0].content.parts &&
        responseJson.candidates[0].content.parts.length > 0 && responseJson.candidates[0].content.parts[0].text) {
        try {
          const parsedAnswer = JSON.parse(responseJson.candidates[0].content.parts[0].text);
          return parsedAnswer.answer;
        } catch (parseError) {
          Log.error(`answerQuestionFromCollection: Failed to parse nested JSON from Gemini response: ${parseError.message}`, { stack: parseError.stack, responseBody: responseBody, question: question });
          return null;
        }
      } else {
        Log.error(`answerQuestionFromCollection: Gemini API response did not contain expected content structure.`, { responseBody: responseBody, question: question });
        return null;
      }
    } else {
      Log.warn(`answerQuestionFromCollection: API Error. Code: ${responseCode}. Body: ${responseBody}`, { responseCode: responseCode, responseBody: responseBody, question: question });
      return null;
    }
  } catch (e) {
    Log.error(`answerQuestionFromCollection: Exception during API call: ${e.message}`, { stack: e.stack, question: question });
    throw e;
  }
}

/******************************************************************************
 *
 * Convenience Function for Question Answering
 *
 * This is a simplified wrapper around answerQuestionFromCollection that
 * automatically uses the clustered chat summaries collection.
 *
 * @param {string} question The user's question.
 * @return {string|null} The answer to the question if found, "NOT_A_QUESTION" if
 *   the input is not a question, "NOT_FOUND" if the answer is not in the context,
 *   or null if an error occurs.
 ******************************************************************************/
function answerQuestionFromChatSummaries(question) {
  return answerQuestionFromCollection(CLUSTERED_COLLECTION_NAME, question);
}

/******************************************************************************
 *
 * "Reduce" Function: Consolidate Topic Names
 *
 * This function takes a list of all topic names generated from the "Map"
 * stage and asks Gemini to consolidate them, merging similar or overlapping
 * topics into a single canonical topic.
 *
 * @param {Array<string>} topicNames An array of topic names.
 * @return {Object|null} A mapping object from old topic names to new,
 *   consolidated names, or null on error.
 * @private
 ******************************************************************************/
function consolidateClusterTopics_(topicNames) {
  const API_KEY = GEMINI_API_KEY;
  if (!API_KEY) {
    Log.error('Error: GEMINI_API_KEY not found in Config.js.');
    return null;
  }

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${API_KEY}`;

  const responseSchema = {
    "type": "OBJECT",
    "properties": {
      "topic_map": {
        "type": "ARRAY",
        "items": {
          "type": "OBJECT",
          "properties": {
            "original_topic": { "type": "STRING" },
            "consolidated_topic": { "type": "STRING" }
          },
          "required": ["original_topic", "consolidated_topic"]
        }
      }
    },
    "required": ["topic_map"]
  };

  const promptText = `
    You are an expert data analyst. Your task is to consolidate the following list of topic labels.
    Group similar or overlapping topics into a single, canonical topic name.
    For example, 'Payment Issues' and 'Billing Inquiries' should both be mapped to a single topic like 'Billing & Payments'.
    
    Return a JSON object that strictly adheres to the provided schema, mapping each original topic to its new consolidated topic name.

    TOPIC LIST:
    ${JSON.stringify(topicNames)}
  `;

  const payload = {
    "contents": [{
      "parts": [{
        "text": promptText
      }]
    }],
    "generationConfig": {
      "response_mime_type": "application/json",
      "response_schema": responseSchema,
      "temperature": 0.0
    }
  };

  const options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };

  try {
    const response = UrlFetchApp.fetch(API_URL, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode >= 200 && responseCode < 300) {
      const parsedResponse = JSON.parse(responseBody);
      const topicMap = {};
      if (parsedResponse.topic_map) {
        parsedResponse.topic_map.forEach(item => {
          topicMap[item.original_topic] = item.consolidated_topic;
        });
      }
      return topicMap;
    } else {
      // For API errors (e.g., 429, 500), return null to trigger retry
      Log.warn(`_consolidateClusterTopics: API Error. Code: ${responseCode}. Body: ${responseBody}`, { responseCode: responseCode, responseBody: responseBody, topicNames: topicNames });
      return null;
    }
  } catch (e) {
    // For network exceptions, re-throw to be caught by the retry mechanism
    Log.error(`_consolidateClusterTopics: Exception during API call: ${e.message}`, { stack: e.stack, topicNames: topicNames });
    throw e;
  }
}
