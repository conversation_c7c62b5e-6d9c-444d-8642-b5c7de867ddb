// Define your GCP Project ID here. This is used by the logging library.
const GCP_PROJECT_ID = 'rzager-chatbot'; // IMPORTANT: Replace with your actual GCP Project ID

/**
 * IMPORTANT: This global Log instance MUST be initialized once per project
 * in a file like Logging.js. Other script files should then use this global instance.
 * This assumes the structured logging library (ID: 1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u)
 * has been added to your Apps Script project and configured via `configureLogger()`.
 */
const Log = Logger.getInstance(PropertiesService.getScriptProperties());

/**
 * One-time setup function to be run manually from the Apps Script editor.
 * This function links your Apps Script project to your Google Cloud Project
 * for structured logging by saving the GCP_PROJECT_ID to Script Properties.
 *
 * After running this function once, the global 'Log' instance (defined above)
 * will be correctly configured.
 */
function configureLogger() {
  Logger.setup(GCP_PROJECT_ID, PropertiesService.getScriptProperties());
  // Use console.log here as the global Log might not be fully configured yet during setup run.
  console.log('Logger setup complete. The global Log instance should now be configured.');
  console.log('View logs at: ' + Log.getLogsUrl()); // Use the global Log instance to get URL
}
