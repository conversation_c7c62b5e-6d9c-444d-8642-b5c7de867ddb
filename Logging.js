// Define your GCP Project ID here. This is used by the logging library.
const GCP_PROJECT_ID = 'rzager-chatbot'; // IMPORTANT: Replace with your actual GCP Project ID

/**
 * IMPORTANT: This global Log instance MUST be initialized once per project
 * in a file like Logging.js. Other script files should then use this global instance.
 * This assumes the structured logging library (ID: 1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u)
 * has been added to your Apps Script project and configured via `configureLogger()`.
 */
let Log;

/**
 * Initialize the Log instance with automatic configuration if needed.
 * This function attempts to get a configured logger instance, and if that fails,
 * it automatically runs the configuration setup.
 */
function initializeLogger() {
  try {
    // First, try to get an existing configured logger instance
    Log = Logger.getInstance(PropertiesService.getScriptProperties());

    // Test if the logger is properly configured by checking if it has a valid project ID
    // This will throw an error if the logger isn't configured
    if (!Log || !Log.getLogsUrl) {
      throw new Error('Logger instance not properly configured');
    }

    // Try to get the logs URL as a configuration test (but don't log it to avoid duplication)
    const logsUrl = Log.getLogsUrl();
    console.log('Logger successfully initialized with existing configuration.');

  } catch (error) {
    console.log('Logger not configured or failed to initialize. Running automatic configuration...');
    console.log('Error details:', error.message);

    try {
      // Automatically run the configuration
      Logger.setup(GCP_PROJECT_ID, PropertiesService.getScriptProperties());

      // Now try to get the configured instance
      Log = Logger.getInstance(PropertiesService.getScriptProperties());

      console.log('Logger automatically configured and initialized successfully.');
      console.log('View logs at: ' + Log.getLogsUrl());

    } catch (setupError) {
      console.error('Failed to automatically configure logger:', setupError.message);
      // Fall back to console logging if structured logging fails
      Log = {
        debug: console.log,
        info: console.log,
        warn: console.warn,
        error: console.error,
        critical: console.error,
        getLogsUrl: () => 'Logger not configured - using console fallback'
      };
      console.warn('Using console fallback for logging due to configuration failure.');
    }
  }
}

// Initialize the logger when this file loads
initializeLogger();

/**
 * Manual setup function that can be run from the Apps Script editor.
 * This function links your Apps Script project to your Google Cloud Project
 * for structured logging by saving the GCP_PROJECT_ID to Script Properties.
 *
 * Note: This function is now optional since initializeLogger() will automatically
 * run the configuration if needed. However, it can still be used for manual setup.
 */
function configureLogger() {
  try {
    Logger.setup(GCP_PROJECT_ID, PropertiesService.getScriptProperties());
    console.log('Manual logger setup complete.');

    // Reinitialize the global Log instance with the new configuration
    initializeLogger();

  } catch (error) {
    console.error('Failed to configure logger manually:', error.message);
    throw error;
  }
}
